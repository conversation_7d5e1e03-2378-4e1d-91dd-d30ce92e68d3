import React, { useState, useEffect, useRef } from "react";
import "../../../styles/Log.css";
import RestoreIcon from "../../../assets/archive-restore.svg";
import user1 from "../../../assets/user1.png";
import Restore from "../../components/Restore";
import { getCurrentUserEndpoints } from "../../../api/endpoints";
import { PERSONAL_NOTES_ENDPOINTS } from "../../../api/endpoints";
import { restorePermanentTask } from "../../../api/admin";
import HistoryIcon from "../../../assets/activitylog.svg";
import SearchIcon from "../../../assets/search.svg";
import RefreshIcon from "../../../assets/refresh-ccw.svg";
import DropdownIcon from "../../../assets/icon-sidebar/dropdown.svg";
import CreationDateIcon from "../../../assets/creationdate.svg";
import EndDateIcon from "../../../assets/enddate.svg";
import { showSuccess, showError } from "../../../utils/toastUtils";

// Thêm cache và preload ở đầu file
let logCache = null;
let logCacheTimestamp = 0;
const LOG_CACHE_DURATION = 10000; // 10s
let logPreloadPromise = null;

const preloadLogs = async (fetchFn) => {
  if (logPreloadPromise) return logPreloadPromise;
  logPreloadPromise = (async () => {
    try {
      const data = await fetchFn();
      logCache = data;
      logCacheTimestamp = Date.now();
      return data;
    } catch {
      return [];
    }
  })();
  return logPreloadPromise;
};

const Log = () => {
  const [openRestore, setOpenRestore] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);
  const [activities, setActivities] = useState(logCache || []);
  const [loading, setLoading] = useState(!logCache);
  const [error, setError] = useState(null);
  const [actionTypes, setActionTypes] = useState([]);
  const [restoredIds, setRestoredIds] = useState(new Set());
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState({
    searchUser: "",
    action: "Tất cả hành động",
    dateFrom: "",
    dateTo: ""
  });
  const [openActionDropdown, setOpenActionDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const dateFromRef = useRef(null);
  const dateToRef = useRef(null);

  const endpoints = getCurrentUserEndpoints();

  // Helper: Lấy token
  const getAuthToken = () => {
    try {
      const tokenFromStorage = localStorage.getItem('token');
      const authToken = localStorage.getItem('authToken');
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const userToken = user.token;
      return tokenFromStorage || authToken || userToken;
    } catch {
      return null;
    }
  };

  // Helper: Tạo headers
  const getAuthHeaders = () => {
    const token = getAuthToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  };

  // Helper: Tạo query parameters
  const buildQueryParams = (page = 1, limit = 50) => {
    const params = new URLSearchParams();

    // Pagination
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    // Filters
    if (filters.searchUser) {
      params.append('searchUser', filters.searchUser);
    }
    if (filters.action && filters.action !== "Tất cả hành động") {
      params.append('action', filters.action);
    }
    if (filters.dateFrom) {
      params.append('dateFrom', filters.dateFrom);
    }
    if (filters.dateTo) {
      params.append('dateTo', filters.dateTo);
    }

    return params.toString();
  };

  // Fetch activities data với pagination và filters
  const fetchActivities = async (page = 1, limit = 50, resetFilters = false) => {
    try {
      setLoading(!logCache);
      setError(null);
      
      const queryParams = buildQueryParams(page, limit);
      const url = `${endpoints.ALL_ACTIVITIES}?${queryParams}`;
      
      // Fetching activities from API
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
              // Activities response received

      // Xử lý response data
      let activitiesData = [];
      let paginationData = { page: 1, limit: 50, total: 0, totalPages: 0 };

      if (data.success && Array.isArray(data.data)) {
        activitiesData = data.data;
        if (data.pagination) {
            paginationData = {
                page: data.pagination.page || 1,
                limit: data.pagination.limit || 50,
                total: data.pagination.total || 0,
                totalPages: data.pagination.pages || Math.ceil(data.pagination.total / (data.pagination.limit || 50)) || 1
            };
        } else {
            paginationData = {
                page: 1,
                limit: activitiesData.length,
                total: activitiesData.length,
                totalPages: 1
            };
        }
      } else if (data.success === false) {
          throw new Error(data.message || 'Có lỗi xảy ra khi tải dữ liệu.');
      } else if (Array.isArray(data)) { // Fallback for old array-based response
          activitiesData = data;
          paginationData = { page: 1, limit: data.length, total: data.length, totalPages: 1 };
      }


      // Lấy các loại action duy nhất để filter
      const uniqueActions = [...new Set(activitiesData.map(a => a.action).filter(Boolean))];
      setActionTypes(uniqueActions);

      // Validate và enrich data
      const enrichedActivities = activitiesData.map(activity => {
        const user = activity.userId || {};
        
        let objectType = activity.resourceType || activity.objectType;
        const actionStr = (activity.action || '').toUpperCase();

        // Cải thiện logic suy luận objectType nếu nó không rõ ràng
        if (!objectType || objectType === 'other' || objectType === 'SYSTEM') {
          if (actionStr.includes('PROJECT')) objectType = 'project';
          else if (actionStr.includes('TASK')) objectType = 'task';
          else if (actionStr.includes('USER')) objectType = 'user';
          else if (actionStr.includes('NOTE')) objectType = 'note';
          else objectType = 'SYSTEM';
        }

        // Cải thiện logic lấy objectId, có thể lấy từ details nếu cần
        let objectId = activity.resourceId || activity.objectId || activity.object_id;
        if (!objectId && activity.details && typeof activity.details === 'string') {
            // Cố gắng tìm một chuỗi 24 ký tự hex (khả năng cao là MongoDB ID) trong chuỗi details
            const match = activity.details.match(/[a-f0-9]{24}/i);
            if (match) {
                objectId = match[0]; // Lấy chuỗi ID đầu tiên tìm thấy
            }
        }
        if (!objectId && activity.resource) {
            objectId = activity.resource.id || activity.resource._id;
        }

        return {
          _id: activity._id || activity.id || Math.random().toString(36).substr(2, 9),
          action: activity.action || 'UNKNOWN',
          objectType: objectType,
          objectId: objectId || null, // Đảm bảo luôn là null nếu không tìm thấy
          userAgent: { // This is the user info object
            fullName: user.fullName || 'Người dùng không xác định',
            email: user.email || 'N/A',
            avatar: user.avatar || null,
            role: user.role || 'N/A',
            department: user.departmentId?.name || user.department?.name || user.department || 'N/A'
          },
          result: activity.details || activity.result || activity.description || activity.message || '',
          ip: activity.ip || activity.ipAddress || 'N/A',
          createdAt: activity.createdAt || activity.created_at || activity.timestamp || new Date().toISOString(),
          updatedAt: activity.updatedAt || activity.updated_at || activity.createdAt || new Date().toISOString(),
          isRestored: false // Thêm trạng thái để theo dõi việc khôi phục
        };
      });

      setActivities(enrichedActivities);
      setPagination(paginationData);
      logCache = enrichedActivities;
      logCacheTimestamp = Date.now();
      
              // Processed activities and pagination data

    } catch (err) {
      console.error('Error fetching activities:', err);
      setError(err.message || 'Không thể tải dữ liệu hoạt động');
      setActivities([]);
      setPagination({
        page: 1,
        limit: 50,
        total: 0,
        totalPages: 0
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let ignore = false;
    const loadLogs = async () => {
      const now = Date.now();
      if (logCache && (now - logCacheTimestamp) < LOG_CACHE_DURATION) {
        setActivities(logCache);
        setLoading(false);
        fetchActivities(); // preload in background
        return;
      }
      setLoading(!logCache);
      await fetchActivities();
    };
    loadLogs();
    return () => { ignore = true; };
  }, []);

  // Handle click outside dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenActionDropdown(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Debounced filter effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchActivities(1, pagination.limit, true);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filters]);

  const handleRefresh = () => {
    fetchActivities(pagination.page, pagination.limit);
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchActivities(newPage, pagination.limit);
    }
  };

  const handleOpenRestore = (activity) => {
    setSelectedLog(activity);
    setOpenRestore(true);
  };

  const handleCancelRestore = () => {
    setOpenRestore(false);
    setSelectedLog(null);
  };

  const handleConfirmRestore = async () => {
    if (!selectedLog?.objectId) {
      showError('Không tìm thấy ID đối tượng để khôi phục');
      return;
    }
    
    try {
      let restoreUrl;
      const objectType = selectedLog.objectType?.toLowerCase();
      const isPermanentDelete = selectedLog.action?.toUpperCase().includes('PERMANENT');

      // Restoring object
      console.log('Restoring object:', {
        objectId: selectedLog.objectId,
        objectType: objectType,
        isPermanent: isPermanentDelete,
        selectedLog: selectedLog
      });
      
      switch (objectType) {
        case 'project':
          restoreUrl = isPermanentDelete
            ? endpoints.RESTORE_PERMANENT_PROJECT(selectedLog.objectId)
            : endpoints.RESTORE_PROJECT(selectedLog.objectId);
          break;
        case 'task':
          if (isPermanentDelete) {
            // Gọi hàm API khôi phục task vĩnh viễn
            const result = await restorePermanentTask(selectedLog.objectId);
            if (result.success !== false) {
              setOpenRestore(false);
              setSelectedLog(null);
              fetchActivities(pagination.page, pagination.limit);
              showSuccess('Khôi phục công việc thành công!');
            } else {
              throw new Error(result.message || 'Có lỗi xảy ra khi khôi phục công việc');
            }
            return;
          }
          // Task không có khôi phục vĩnh viễn trong endpoints.js
          const projectId = selectedLog.projectId || selectedLog.objectId.split('_')[0] || 'default';
          restoreUrl = endpoints.RESTORE_TASK(projectId, selectedLog.objectId);
          break;
        case 'user':
           restoreUrl = isPermanentDelete
            ? endpoints.RESTORE_PERMANENT_USER(selectedLog.objectId)
            : endpoints.RESTORE_USER(selectedLog.objectId);
          break;
        case 'note':
          // Ghi chú không có khôi phục vĩnh viễn
          restoreUrl = `${PERSONAL_NOTES_ENDPOINTS.GET_NOTES}/${selectedLog.objectId}/restore`;
          break;
        default:
          // Fallback cho các loại đối tượng khác
          console.warn(`Unknown object type: ${objectType}, using project restore as fallback`);
          restoreUrl = endpoints.RESTORE_PROJECT(selectedLog.objectId);
          break;
      }
      
      if (!restoreUrl) {
        throw new Error(`Không hỗ trợ khôi phục loại đối tượng: ${objectType}`);
      }

      // Making restore request

      const response = await fetch(restoreUrl, {
        method: 'PUT',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Restore response error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      // Restore response received
      
      if (result.success !== false) {
        // Cập nhật trạng thái cục bộ để UI phản hồi ngay lập tức
        setActivities(currentActivities =>
          currentActivities.map(act =>
            act._id === selectedLog._id ? { ...act, isRestored: true } : act
          )
        );
        setOpenRestore(false);
        setSelectedLog(null);
        
        // Thêm ID vào danh sách đã khôi phục và fetch lại dữ liệu
        setRestoredIds(prev => new Set(prev).add(selectedLog._id));
        fetchActivities(pagination.page, pagination.limit);
        
        // Thông báo thành công dựa trên loại đối tượng
        const objectTypeText = {
          'project': 'dự án',
          'task': 'công việc',
          'user': 'người dùng',
          'note': 'ghi chú'
        }[objectType] || 'đối tượng';
        
        showSuccess(`Khôi phục ${objectTypeText} thành công!`);
      } else {
        throw new Error(result.message || `Có lỗi xảy ra khi khôi phục ${objectType}`);
      }
    } catch (err) {
      console.error('Error restoring object:', err);
      showError(err.message || 'Có lỗi xảy ra khi khôi phục đối tượng');
    }
  };

  // Hàm mapping action sang tiếng Việt
  const translateAction = (action) => {
    const actionStr = (action || '').toUpperCase();
    
    const actionMapping = {
      'ADMIN_LOGIN': 'Đăng nhập Admin',
      'ADMIN_FORCE_LOGOUT': 'Đăng xuất cưỡng chế Admin',
      'ADMIN_MARK_ALL_NOTIFICATIONS_READ': 'Đánh dấu tất cả thông báo đã đọc',
      'ADMIN_MARK_NOTIFICATION_READ': 'Đánh dấu thông báo đã đọc',
      'ADMIN_MARK_NOTIFICATIONS_READ': 'Đánh dấu thông báo đã đọc',
      'ADMIN_MARK_ALL_NOTIFICATIONS_UNREAD': 'Đánh dấu tất cả thông báo chưa đọc',
      'ADMIN_MARK_NOTIFICATION_UNREAD': 'Đánh dấu thông báo chưa đọc',
      'ADMIN_MARK_NOTIFICATIONS_UNREAD': 'Đánh dấu thông báo chưa đọc',
      'VIEW_STAFF_TASK_ASSIGNMENT_STATS': 'Xem thống kê phân công công việc nhân viên',
      'VIEW_ALL_DEPARTMENTS_PROJECT_STATS': 'Xem thống kê dự án tất cả phòng ban',
      'CREATE_PROJECT': 'Tạo dự án',
      'UPDATE_PROJECT': 'Cập nhật dự án',
      'DELETE_PROJECT': 'Xóa dự án',
      'CREATE_TASK': 'Tạo công việc',
      'UPDATE_TASK': 'Cập nhật công việc',
      'DELETE_TASK': 'Xóa công việc',
      'CREATE_USER': 'Tạo người dùng',
      'UPDATE_USER': 'Cập nhật người dùng',
      'DELETE_USER': 'Xóa người dùng',
      'ADD_MEMBER': 'Thêm thành viên',
      'REMOVE_MEMBER': 'Xóa thành viên',
      'ASSIGN_TASK': 'Giao việc',
      'COMPLETE_TASK': 'Hoàn thành công việc',
      'BLOCK_USER': 'Chặn người dùng',
      'UNBLOCK_USER': 'Bỏ chặn người dùng',
      'CHANGE_ROLE': 'Thay đổi vai trò',
      'VIEW_PROJECT': 'Xem dự án',
      'VIEW_TASK': 'Xem công việc',
      'VIEW_USER': 'Xem người dùng',
      'RESTORE_PROJECT': 'Khôi phục dự án',
      'RESTORE_TASK': 'Khôi phục công việc',
      'RESTORE_USER': 'Khôi phục người dùng',
      'LOGIN': 'Đăng nhập',
      'LOGOUT': 'Đăng xuất',
      'CREATE': 'Tạo mới',
      'UPDATE': 'Cập nhật',
      'DELETE': 'Xóa',
      'DESTROY': 'Xóa',
      'VIEW': 'Xem',
      'COMPLETE': 'Hoàn thành',
      'BLOCK': 'Chặn',
      'UNBLOCK': 'Bỏ chặn',
      'RESTORE': 'Khôi phục'
    };

    // Tìm mapping chính xác trước
    if (actionMapping[actionStr]) {
      return actionMapping[actionStr];
    }

    // Nếu không tìm thấy, tìm partial match
    for (const [key, value] of Object.entries(actionMapping)) {
      if (actionStr.includes(key)) {
        return value;
      }
    }

    // Fallback: trả về action gốc nếu không tìm thấy mapping
    return action;
  };

  const getStatusBadge = (action, result = '', canRestore = false, isRestored = false) => {
    const actionStr = (action || '').toUpperCase();
    const resultStr = (result || '').toLowerCase();

    // Nếu đã khôi phục, luôn hiển thị trạng thái này trước
    if (isRestored) {
      return { text: 'Đã khôi phục', class: 'status-restored' };
    }

    // Quy tắc 1: Nếu có nút Khôi phục, trạng thái luôn là "Xóa".
    if (canRestore) {
      return { text: 'Xóa', class: 'status-deleted' };
    }

    // Quy tắc 2: Nếu không có nút, kiểm tra xem có phải log "Khôi phục" không.
    if (resultStr.includes('khôi phục') || actionStr.includes('RESTORE')) {
      return { text: 'Đã khôi phục', class: 'status-restored' };
    }
    
    // Các trạng thái còn lại
    if (actionStr.includes('CREATE')) return { text: 'Tạo mới', class: 'status-created' };
    if (actionStr.includes('UPDATE')) return { text: 'Cập nhật', class: 'status-updated' };
    if (actionStr.includes('DELETE') || actionStr.includes('DESTROY')) return { text: 'Xóa', class: 'status-deleted' };
    if (actionStr.includes('LOGIN')) return { text: 'Đăng nhập', class: 'status-login' };
    if (actionStr.includes('LOGOUT')) return { text: 'Đăng xuất', class: 'status-logout' };
    if (actionStr.includes('ADD_MEMBER')) return { text: 'Thêm thành viên', class: 'status-member-added' };
    if (actionStr.includes('REMOVE_MEMBER')) return { text: 'Xóa thành viên', class: 'status-member-removed' };
    if (actionStr.includes('COMPLETE')) return { text: 'Hoàn thành', class: 'status-completed' };
    if (actionStr.includes('BLOCK')) return { text: 'Chặn', class: 'status-blocked' };
    if (actionStr.includes('UNBLOCK')) return { text: 'Bỏ chặn', class: 'status-unblocked' };
    if (actionStr.includes('CHANGE_ROLE')) return { text: 'Thay đổi vai trò', class: 'status-role-changed' };
    if (actionStr.includes('ASSIGN_TASK')) return { text: 'Giao việc', class: 'status-task-assigned' };
    if (actionStr.includes('MARK_ALL_NOTIFICATIONS_READ')) return { text: 'Đánh dấu tất cả đã đọc', class: 'status-notification-read' };
    if (actionStr.includes('MARK_NOTIFICATION_READ') || actionStr.includes('MARK_NOTIFICATIONS_READ')) return { text: 'Đánh dấu đã đọc', class: 'status-notification-read' };
    if (actionStr.includes('MARK_ALL_NOTIFICATIONS_UNREAD')) return { text: 'Đánh dấu tất cả chưa đọc', class: 'status-notification-unread' };
    if (actionStr.includes('MARK_NOTIFICATION_UNREAD') || actionStr.includes('MARK_NOTIFICATIONS_UNREAD')) return { text: 'Đánh dấu chưa đọc', class: 'status-notification-unread' };
    if (actionStr.includes('VIEW')) return { text: 'Xem', class: 'status-login' };

    return { text: action, class: 'status-default' };
  };

  const getPositionText = (position) => {
    const positions = {
      'FE': 'Frontend Developer',
      'BE': 'Backend Developer',
      'BA': 'Business Analyst',
      'PM': 'Project Manager',
      'Designer': 'Designer',
      'QA': 'Quality Assurance',
      'DevOps': 'DevOps Engineer'
    };
    return positions[position] || position;
  };

  const getDepartmentText = (department) => {
    const departments = {
      'IT': 'IT',
      'Marketing': 'Marketing',
      'Accounting': 'Kế Toán',
      'HCNS': 'Hành Chính Nhân Sự'
    };
    return departments[department] || department;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      
      return date.toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '';
    }
  };

  // Filter activities based on search criteria (client-side filtering as backup)
  const filteredActivities = activities.filter(activity => {
    const matchesUser = filters.searchUser === "" ||
      (activity.userAgent?.fullName || activity.userAgent?.email || '').toLowerCase().includes(filters.searchUser.toLowerCase());

    const matchesAction = filters.action === "Tất cả hành động" ||
      activity.action === filters.action;

    const matchesDateFrom = !filters.dateFrom ||
      new Date(activity.createdAt) >= new Date(filters.dateFrom);

    const matchesDateTo = !filters.dateTo ||
      new Date(activity.createdAt) <= new Date(filters.dateTo + 'T23:59:59');

    return matchesUser && matchesAction && matchesDateFrom && matchesDateTo;
  });

  return (
    <div className="log-activity-log-container">
            {/* Header và Filter section gộp chung */}
            <div className="log-activity-log-header-filters">
              {/* Header với title */}
              <div className="log-activity-log-header">
                <div className="log-activity-log-title">
                  <img src={HistoryIcon} alt="history" style={{ width: 24, height: 24}} />
                  <h2>Nhật ký hoạt động</h2>
                  {pagination.total > 0 && (
                    <span className="log-activity-count">({pagination.total} hoạt động)</span>
                  )}
                </div>
                <button className="log-refresh-btn" onClick={handleRefresh} disabled={loading}>
                  <img src={RefreshIcon} alt="refresh" style={{ width: 16, height: 16 }} />
                  {loading ? 'Đang tải...' : 'Làm mới'}
                </button>
              </div>

              {/* Filter section */}
              <div className="log-activity-log-filters">
                <div className="log-filter-row">
                  <div className="log-filter-group">
                    <label>Tìm người thực hiện</label>
                    <div className="log-search-input-wrapper">
                      <input
                        type="text"
                        value={filters.searchUser}
                        onChange={(e) => setFilters({...filters, searchUser: e.target.value})}
                        placeholder="Nhập tên..."
                        className="log-search-input"
                      />
                      <img src={SearchIcon} alt="search" className="log-search-icon" />
                    </div>
                  </div>
                  <div className="log-filter-group">
                    <label>Hành động</label>
                    <div className={`log-activity-log-dropdown ${openActionDropdown ? 'open' : ''}`} ref={dropdownRef}>
                                             <button
                         className="log-activity-log-dropdown-btn"
                         onClick={() => setOpenActionDropdown(!openActionDropdown)}
                       >
                         <span>{filters.action === "Tất cả hành động" ? "Tất cả hành động" : translateAction(filters.action)}</span>
                         <img src={DropdownIcon} alt="dropdown" className="log-dropdown-icon" />
                       </button>
                      {openActionDropdown && (
                        <div className="log-activity-log-dropdown-menu">
                          <div
                            className="log-activity-log-dropdown-item"
                            onClick={() => {
                              setFilters({...filters, action: "Tất cả hành động"});
                              setOpenActionDropdown(false);
                            }}
                          >
                            Tất cả hành động
                          </div>
                          {actionTypes.map(action => (
                            <div
                              key={action}
                              className="log-activity-log-dropdown-item"
                              onClick={() => {
                                setFilters({...filters, action: action});
                                setOpenActionDropdown(false);
                              }}
                            >
                              {translateAction(action)}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="log-filter-group">
                    <label>Từ ngày</label>
                    <div
                      className="log-date-input-wrapper"
                      onClick={() => {
                        if (dateFromRef.current) {
                          dateFromRef.current.focus();
                          dateFromRef.current.showPicker?.();
                        }
                      }}
                    >
                      <img src={CreationDateIcon} alt="start date" className="log-date-icon" />
                      <input
                        ref={dateFromRef}
                        type="date"
                        value={filters.dateFrom}
                        onChange={(e) => setFilters({...filters, dateFrom: e.target.value})}
                        className="log-date-input-field"
                        placeholder="Từ ngày"
                      />
                    </div>
                  </div>
                  <div className="log-filter-group">
                    <label>Đến ngày</label>
                    <div
                      className="log-date-input-wrapper"
                      onClick={() => {
                        if (dateToRef.current) {
                          dateToRef.current.focus();
                          dateToRef.current.showPicker?.();
                        }
                      }}
                    >
                      <img src={EndDateIcon} alt="end date" className="log-date-icon" />
                      <input
                        ref={dateToRef}
                        type="date"
                        value={filters.dateTo}
                        onChange={(e) => setFilters({...filters, dateTo: e.target.value})}
                        className="log-date-input-field"
                        placeholder="Đến ngày"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Activity log list */}
            <div className="log-activity-log-list">
              {loading ? (
                Array.from({ length: 6 }).map((_, idx) => (
                  <div key={idx} className="log-activity-log-item-new" style={{ opacity: 0.7 }}>
                    <div className="log-log-avatar">
                      <div style={{ width: 40, height: 40, borderRadius: '50%', background: '#f0f0f0', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                    <div className="log-log-content" style={{ flex: 1 }}>
                      <div style={{ width: '60%', height: 18, background: '#f0f0f0', borderRadius: 4, marginBottom: 8, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: '40%', height: 14, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: '80%', height: 12, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: '50%', height: 12, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                  </div>
                ))
              ) : error ? (
                <div className="log-error-container">
                  <p className="log-error-message">{error}</p>
                  <button onClick={handleRefresh} className="log-retry-btn">Thử lại</button>
                </div>
              ) : filteredActivities.length === 0 ? (
                <div className="log-empty-container">
                  <p>Không có hoạt động nào được tìm thấy</p>
                </div>
              ) : (
                <>
                  {filteredActivities.map((activity, idx) => {
                    const isRestored = restoredIds.has(activity._id);
                    const isRestorableAction = (activity.action.includes('DELETE') || activity.action.includes('DESTROY')) && !!activity.objectId;
                    const canRestore = isRestorableAction && !isRestored;
                    const status = getStatusBadge(activity.action, activity.result, canRestore, isRestored);
                    const positionText = getPositionText(activity.userAgent?.role || activity.userAgent?.position || 'N/A');
                    const departmentText = getDepartmentText(activity.userAgent?.department || 'N/A');
                    const userInfo = activity.userAgent || {};

                    return (
                      <div key={activity._id || idx} className="log-activity-log-item-new">
                        <div className="log-log-avatar">
                          <img
                            src={userInfo.avatar || user1}
                            alt="user"
                            onError={(e) => { e.target.src = user1; }}
                          />
                        </div>
                        <div className="log-log-content">
                          <div className="log-log-header">
                            <div className="log-log-user-info">
                              <span className="log-user-name">
                                {userInfo.fullName || userInfo.email || 'Người dùng không xác định'}
                              </span>
                              <span className={`log-status-badge log-${status.class}`}>{status.text}</span>
                              <span className="log-log-date">{formatDate(activity.createdAt)}</span>
                            </div>
                            <div className="log-log-actions">
                              {isRestorableAction && (
                                isRestored ? (
                                  <button
                                    className="log-activity-log-restore-btn restored"
                                    disabled
                                  >
                                    Đã khôi phục
                                  </button>
                                ) : (
                                  <button
                                    className="log-activity-log-restore-btn"
                                    onClick={() => handleOpenRestore(activity)}
                                  >
                                    <img src={RestoreIcon} alt="restore" style={{ width: 16, height: 16 }} />
                                    Khôi phục
                                  </button>
                                )
                              )}
                            </div>
                          </div>
                          <div className="log-log-details">
                            <div className="log-log-detail-row">
                              <span className="log-detail-label">Vị trí:</span>
                              <span className="log-detail-value">{positionText}</span>
                            </div>
                            <div className="log-log-detail-row">
                              <span className="log-detail-label">Phòng ban:</span>
                              <span className="log-detail-value">{departmentText}</span>
                            </div>
                            <div className="log-log-detail-row">
                              <span className="log-detail-label">Email:</span>
                              <span className="log-detail-value">{userInfo.email || 'N/A'}</span>
                            </div>
                            <div className="log-log-detail-row">
                              <span className="log-detail-label">Vai trò:</span>
                              <span className="log-detail-value">{userInfo.role || 'N/A'}</span>
                            </div>
                            <div className="log-log-detail-row">
                              <span className="log-detail-label">IP:</span>
                              <span className="log-detail-value">{activity.ip || 'N/A'}</span>
                            </div>
                            {activity.objectId && (
                              <div className="log-log-detail-row">
                                <span className="log-detail-label">ID đối tượng:</span>
                                <span className="log-detail-value">{activity.objectId}</span>
                              </div>
                            )}
                          </div>
                                                      <div className="log-log-description">
                              <span className="log-detail-label">Chi tiết:</span>
                              <span className="log-detail-value">
                                {activity.result || `${userInfo.fullName || userInfo.email} đã ${translateAction(activity.action).toLowerCase()}`}
                              </span>
                            </div>
                        </div>
                      </div>
                    );
                  })}
                  
                  {/* Pagination */}
                  {pagination.totalPages > 1 && (
                    <div className="log-pagination-container">
                      <button
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page <= 1}
                        className="log-pagination-btn"
                      >
                        Trước
                      </button>
                      <span className="log-pagination-info">
                        Trang {pagination.page} / {pagination.totalPages}
                      </span>
                      <button
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page >= pagination.totalPages}
                        className="log-pagination-btn"
                      >
                        Sau
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
      <Restore open={openRestore} onCancel={handleCancelRestore} onConfirm={handleConfirmRestore} selectedLog={selectedLog} />
    </div>
  );
};

export default Log;